"use client";

import React, { useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { FaTimes, FaEdit } from "react-icons/fa";
import { useGetModuleById } from "@/api-services/modules/modules";
import { useGetCourseDetail, CourseLesson } from "@/api-services/courses/courses";
import { useDeleteLesson } from "@/api-services/lessons";
import { useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

// Use the CourseLesson interface from the API, but extend it for compatibility
interface Lesson extends CourseLesson {
  quiz_files?: string;
  project_description?: string;
  project_files?: string;
  module?: string;
  created_by?: number;
}

const ViewLessonsPage = () => {
  const [overviewFile, setOverviewFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get courseId and moduleId from URL params
  const courseId = searchParams.get('courseId') || '';
  const moduleId = searchParams.get('moduleId') || '';

  // API hooks
  const { data: moduleResponse, isLoading: moduleLoading, error: moduleError } = useGetModuleById(moduleId);
  const { data: courseDetailResponse, isLoading: courseDetailLoading } = useGetCourseDetail(courseId);
  const deleteLessonMutation = useDeleteLesson();
  const queryClient = useQueryClient();

  // Find the specific module with lessons from course detail
  const moduleWithLessons = courseDetailResponse?.data?.modules?.find(m => m.id === moduleId);
  const lessonsLoading = courseDetailLoading;

  const handleBack = () => router.back();

  const handleAddLesson = () => {
    if (courseId && moduleId) {
      router.push(`/Admin/dashboard/addlession?courseId=${courseId}&moduleId=${moduleId}`);
    } else {
      toast.error('Course ID or Module ID is missing');
    }
  };

  const handleEditLesson = (lessonId: string) => {
    if (courseId && moduleId) {
      router.push(`/Admin/dashboard/addlession?lessonId=${lessonId}&courseId=${courseId}&moduleId=${moduleId}`);
    } else {
      toast.error('Course ID or Module ID is missing');
    }
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setOverviewFile(file);
    }
  };

  const handleRemoveFile = () => {
    setOverviewFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleLessonDelete = async (lessonId: string) => {
    if (!confirm('Are you sure you want to delete this lesson?')) {
      return;
    }

    try {
      await deleteLessonMutation.mutateAsync(lessonId);

      // Invalidate and refetch the course detail to update the lessons list
      queryClient.invalidateQueries({ queryKey: ["course-detail", courseId] });

      toast.success('Lesson deleted successfully!');
    } catch (error: any) {
      console.error('Delete lesson error:', error);
      toast.error(error?.message || 'Failed to delete lesson');
    }
  };

  // Get module data
  const moduleData = moduleResponse?.data;
  const lessons: Lesson[] = moduleWithLessons?.lessons || [];

  if (moduleLoading) {
    return (
      <div className="min-h-screen bg-white p-4">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-32 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (moduleError) {
    return (
      <div className="min-h-screen bg-white p-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Module</h1>
          <p className="text-gray-600 mb-4">Failed to load module details.</p>
          <button
            onClick={handleBack}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="flex items-center gap-4 p-4 border-b">
        <button
          onClick={handleBack}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
        >
          <span className="text-sm">← Back</span>
        </button>
        <div className="flex-1">
          <h1 className="text-lg font-semibold">
            Course_01: {moduleData?.course || 'Python'}
          </h1>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        {/* Module Title */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Module title :</label>
          <input
            type="text"
            value={moduleData?.title || 'Loading...'}
            readOnly
            className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
          />
        </div>

        {/* Upload Overview Text */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Upload Overview Text :</label>

          {/* File Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center mb-4">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 mb-4 flex items-center justify-center bg-gray-100 rounded-full">
                <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <p className="text-gray-500 mb-2">Choose file here</p>
              <p className="text-xs text-gray-400 mb-4">
                Supported file type(s) : .md<br />
                Size limit: 20MB per file(s) with total size not exceeding 100MB
              </p>
              <button
                onClick={handleFileUpload}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Upload
              </button>
            </div>
          </div>

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".md"
            onChange={handleFileChange}
            className="hidden"
          />

          {/* Uploaded File Display */}
          {overviewFile && (
            <div className="flex items-center justify-between border border-gray-300 rounded-md px-4 py-3 mb-4">
              <div className="flex items-center gap-3">
                <span className="text-gray-600 font-medium">1.</span>
                <span className="text-blue-600 underline">{overviewFile.name}</span>
                <span className="text-gray-500 text-sm">
                  ({(overviewFile.size / 1024 / 1024).toFixed(2)} MB)
                </span>
              </div>
              <button
                onClick={handleRemoveFile}
                className="text-red-500 hover:text-red-700"
              >
                <FaTimes />
              </button>
            </div>
          )}

          <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Upload
          </button>
        </div>

        {/* Add Lessons Section */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <h2 className="text-lg font-semibold text-blue-600">Add Lessons :</h2>
            <button
              onClick={handleAddLesson}
              className="flex items-center gap-2 border border-green-600 text-green-600 px-4 py-2 rounded hover:bg-green-600 hover:text-white"
            >
              <span>+</span>
              Add Lesson
            </button>
          </div>

          {/* Lessons Table */}
          {lessonsLoading ? (
            <div className="animate-pulse">
              <div className="h-12 bg-gray-200 rounded mb-2"></div>
              <div className="h-12 bg-gray-200 rounded mb-2"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
            </div>
          ) : lessons.length > 0 ? (
            <div className="border border-gray-300 rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">No</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Lesson Name</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">No. of Quiz</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">No. of Projects</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {lessons.map((lesson: Lesson, index: number) => (
                    <tr key={lesson.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm">{String(index + 1).padStart(2, '0')}</td>
                      <td className="px-4 py-3 text-sm">{lesson.title}</td>
                      <td className="px-4 py-3 text-sm text-center">
                        {lesson.quiz_files ? '1' : '0'}
                      </td>
                      <td className="px-4 py-3 text-sm text-center">
                        {lesson.project_files ? '1' : '0'}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleEditLesson(lesson.id)}
                            className="text-blue-500 hover:text-blue-700"
                            title="Edit lesson"
                          >
                            <FaEdit />
                          </button>
                          <button
                            onClick={() => handleLessonDelete(lesson.id)}
                            disabled={deleteLessonMutation.isPending}
                            className="text-red-500 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Delete lesson"
                          >
                            {deleteLessonMutation.isPending ? (
                              <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                            ) : (
                              <FaTimes />
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="border border-gray-300 rounded-lg p-8 text-center text-gray-500">
              No lessons found. Click "Add Lesson" to create your first lesson.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewLessonsPage;
