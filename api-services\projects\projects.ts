import { makeRequest } from "../utils";
import { useMutation } from "@tanstack/react-query";

// ================== PROJECT INTERFACES ==================
interface Project {
  id: string;
  title: string;
  description: string;
  lesson_title: string;
  created_by_name: string;
  created_at: string;
  status: string;
  file_url: string;
  instructions: string;
  due_date: string;
  max_marks: number;
}

interface CreateProjectPayload {
  file_url: string;
  title?: string;
  description?: string;
  instructions?: string;
  max_marks?: number;
}

interface CreateProjectResponse {
  message: string;
  data: Project;
}

// ================== CREATE PROJECT API ==================
async function createProject(lessonId: string, payload: CreateProjectPayload): Promise<CreateProjectResponse> {
  console.log("Calling CREATE project API for lesson:", lessonId, "with payload:", payload);

  if (!lessonId) {
    throw new Error("Lesson ID is required for project creation");
  }

  if (!payload.file_url || !payload.file_url.trim()) {
    throw new Error("Project file URL is required");
  }

  try {
    // Validate URL format
    try {
      new URL(payload.file_url);
    } catch (urlError) {
      throw new Error("Invalid URL format for project file");
    }

    // Prepare the payload with default values
    const projectPayload = {
      file_url: payload.file_url.trim(),
      title: payload.title || "Project based on URL",
      description: payload.description || "",
      instructions: payload.instructions || "",
      max_marks: payload.max_marks || 20,
    };

    console.log("Sending project payload:", projectPayload);

    const response = await makeRequest({
      endpoint: `/api/lessons/${lessonId}/projects/`,
      method: "POST",
      data: projectPayload,
    });

    console.log("CREATE project API response:", response);
    return response;
  } catch (error) {
    console.error("CREATE project API error:", error);
    console.error("Payload that failed:", payload);
    console.error("LessonId:", lessonId);

    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes("404")) {
        throw new Error("Project endpoint not found. Please check the lesson ID.");
      } else if (error.message.includes("403")) {
        throw new Error("Permission denied. Please check your authentication.");
      } else if (error.message.includes("400")) {
        throw new Error("Invalid project data. Please check the URL and try again.");
      } else if (error.message.includes("500")) {
        throw new Error("Server error occurred while creating project. Please try again later.");
      }
    }

    throw error;
  }
}

// ================== REACT QUERY HOOKS ==================
const useCreateProject = () => {
  return useMutation<CreateProjectResponse, Error, { lessonId: string; payload: CreateProjectPayload }>({
    mutationFn: ({ lessonId, payload }) => createProject(lessonId, payload),
    onError: (error) => {
      console.error("Project creation error:", error);
    },
  });
};

// ================== EXPORTS ==================
export {
  useCreateProject,
  type Project,
  type CreateProjectPayload,
  type CreateProjectResponse,
};
